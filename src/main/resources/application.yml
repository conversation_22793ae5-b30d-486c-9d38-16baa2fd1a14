server:
  port: 8083

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      connection-timeout: 6000
      maximum-pool-size: 5
#  jackson:
#    date-format: yyyy-MM-dd HH:mm:ss
#    time-zone: GMT+8
#    serialization:
#      write-dates-as-timestamps: false

mybatis-plus:
  configuration:
    #开启驼峰功能,数据库字段hello_world 实体类helloWolrd 也能对应匹配
    map-underscore-to-camel-case: true
    #结果集自动映射(resultMap)
    auto-mapping-behavior: full
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:mapping/*Mapper.xml
  global-config:
    # 逻辑删除配置
    db-config:
      # 删除前
      logic-not-delete-value: 1
      # 删除后
      logic-delete-value: 0