package org.example.mybatisplus.controller;


import org.example.mybatisplus.service.IEmployeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@RestController
@RequestMapping("/employee")
public class EmployeeController {
    @Autowired
    private IEmployeeService employeeService;
    @RequestMapping("/selectAll")
    public Object selectAll(){
        return null;
    }
    //easyexcel导出
    @RequestMapping("/export")
    public void export(HttpServletResponse response){
        employeeService.export(response);
    }

}
