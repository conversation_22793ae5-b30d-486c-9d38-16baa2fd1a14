package org.example.mybatisplus.controller;


import org.example.mybatisplus.entity.Admin;
import org.example.mybatisplus.entity.Result;
import org.example.mybatisplus.service.IAdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@RestController
@CrossOrigin(origins = {"http://localhost:5173", "http://localhost:5174"})
@RequestMapping("/admin")
public class AdminController {
    @Autowired
    private IAdminService adminService;
    @RequestMapping("/register")
    public Result register(@RequestParam String username,
                          @RequestParam String password,
                          @RequestParam String email)
    {
        Admin admin = new Admin();
        admin.setUsername(username);
        admin.setPassword(password);
        admin.setEmail(email);
        return adminService.register(admin);
    }

    @RequestMapping("/login")
    public Result login(@RequestParam String username,
                       @RequestParam String password)
    {
        Admin admin = new Admin();
        admin.setUsername(username);
        admin.setPassword(password);
        return adminService.login(admin);
    }

    @RequestMapping("/checkLogin")
    public Result checkLogin(@RequestParam String username)
    {
        // 简单的用户存在性检查
        Admin admin = new Admin();
        admin.setUsername(username);
        return adminService.checkLogin(username);
    }

}
