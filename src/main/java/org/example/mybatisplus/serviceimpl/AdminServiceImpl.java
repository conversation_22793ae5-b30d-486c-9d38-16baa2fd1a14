package org.example.mybatisplus.serviceimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.example.mybatisplus.entity.Admin;
import org.example.mybatisplus.entity.Result;
import org.example.mybatisplus.mapper.AdminMapper;
import org.example.mybatisplus.service.IAdminService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.regex.Pattern;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Service
public class AdminServiceImpl extends ServiceImpl<AdminMapper, Admin> implements IAdminService {

    // BCrypt密码加密器
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    // 邮箱格式验证正则表达式
    private static final String EMAIL_PATTERN =
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@" +
        "(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";

    private static final Pattern pattern = Pattern.compile(EMAIL_PATTERN);

    @Override
    public Result register(Admin admin) {
        // 参数校验
        if (admin.getUsername() == null || admin.getUsername().trim().isEmpty()) {
            return Result.fail("用户名不能为空");
        }
        if (admin.getPassword() == null || admin.getPassword().trim().isEmpty()) {
            return Result.fail("密码不能为空");
        }
        if (admin.getEmail() == null || admin.getEmail().trim().isEmpty()) {
            return Result.fail("邮箱不能为空");
        }

        // 邮箱格式验证
        if (!isValidEmail(admin.getEmail().trim())) {
            return Result.fail("邮箱格式不正确");
        }

        // 检查用户名是否已存在
        LambdaQueryWrapper<Admin> usernameQuery = new LambdaQueryWrapper<>();
        Admin existingAdmin = baseMapper.selectOne(usernameQuery.eq(Admin::getUsername, admin.getUsername().trim()));
        if (!Objects.isNull(existingAdmin)) {
            return Result.fail("用户名已存在");
        }

        // 检查邮箱是否已存在
        LambdaQueryWrapper<Admin> emailQuery = new LambdaQueryWrapper<>();
        Admin existingEmailAdmin = baseMapper.selectOne(emailQuery.eq(Admin::getEmail, admin.getEmail().trim()));
        if (!Objects.isNull(existingEmailAdmin)) {
            return Result.fail("邮箱已被注册");
        }

        // 创建新管理员
        Admin newAdmin = new Admin();
        newAdmin.setUsername(admin.getUsername().trim());
        newAdmin.setPassword(passwordEncoder.encode(admin.getPassword())); // BCrypt加密存储
        newAdmin.setEmail(admin.getEmail().trim());

        // 保存到数据库
        if (baseMapper.insert(newAdmin) > 0) {
            return Result.success("注册成功");
        } else {
            return Result.fail("服务器繁忙，请稍后重试");
        }
    }

    @Override
    public Result login(Admin admin) {
        // 参数校验
        if (admin.getUsername() == null || admin.getUsername().trim().isEmpty()) {
            return Result.fail("用户名不能为空");
        }
        if (admin.getPassword() == null || admin.getPassword().trim().isEmpty()) {
            return Result.fail("密码不能为空");
        }

        // 查询管理员
        LambdaQueryWrapper<Admin> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        Admin admin1 = baseMapper.selectOne(lambdaQueryWrapper.eq(Admin::getUsername, admin.getUsername().trim()));
        if (Objects.isNull(admin1)) {
            return Result.fail("用户名或密码错误");
        }

        // BCrypt密码验证
        if (!passwordEncoder.matches(admin.getPassword(), admin1.getPassword())) {
            return Result.fail("用户名或密码错误");
        }

        // 登录成功，返回管理员信息（不包含密码）
        admin1.setPassword(null);
        return Result.success("登录成功", admin1);
    }

    @Override
    public Result checkLogin(String username) {
        if (username == null || username.trim().isEmpty()) {
            return Result.fail("用户名不能为空");
        }

        LambdaQueryWrapper<Admin> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        Admin admin = baseMapper.selectOne(lambdaQueryWrapper.eq(Admin::getUsername, username.trim()));
        if (Objects.isNull(admin)) {
            return Result.success("用户不存在", false);
        }

        return Result.success("用户存在", true);
    }

    /**
     * 验证邮箱格式
     * @param email 邮箱
     * @return 是否有效
     */
    private boolean isValidEmail(String email) {
        return pattern.matcher(email).matches();
    }
}
