package org.example.mybatisplus.serviceimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.example.mybatisplus.entity.Employee;
import org.example.mybatisplus.mapper.EmployeeMapper;
import org.example.mybatisplus.service.IEmployeeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Service
public class EmployeeServiceImpl extends ServiceImpl<EmployeeMapper, Employee> implements IEmployeeService {
    //导出Excel
    @Override
    public void export(HttpServletResponse response) {
        List employees = baseMapper.selectList(new LambdaQueryWrapper<Employee>());
        System.out.println(employees);
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet("员工信息");
        Row header = sheet.createRow(0);
        header.createCell(0).setCellValue("员工ID");

    }

}
