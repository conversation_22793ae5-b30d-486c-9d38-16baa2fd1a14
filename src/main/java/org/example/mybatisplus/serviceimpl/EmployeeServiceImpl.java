package org.example.mybatisplus.serviceimpl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.example.mybatisplus.entity.Employee;
import org.example.mybatisplus.mapper.EmployeeMapper;
import org.example.mybatisplus.service.IEmployeeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Service
public class EmployeeServiceImpl extends ServiceImpl<EmployeeMapper, Employee> implements IEmployeeService {
    //导出Excel
    @Override
    public void export(HttpServletResponse resp) {
        List employees = baseMapper.selectList(new LambdaQueryWrapper<Employee>());
        System.out.println(employees);
        // 设置文本内省
        resp.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        // 设置字符编码
        resp.setCharacterEncoding("utf-8");
        // 设置响应头
        resp.setHeader("Content-disposition", "attachment;filename=demo.xlsx");
        try {
            EasyExcel.write(resp.getOutputStream(), Employee.class).sheet("成员列表").doWrite(employees);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


    }

}
