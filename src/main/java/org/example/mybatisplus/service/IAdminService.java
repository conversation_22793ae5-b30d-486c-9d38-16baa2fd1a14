package org.example.mybatisplus.service;

import org.example.mybatisplus.entity.Admin;
import com.baomidou.mybatisplus.extension.service.IService;
import org.example.mybatisplus.entity.Result;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
public interface IAdminService extends IService<Admin> {
    Result register(Admin admin);
    Result login(Admin admin);
    Result checkLogin(String username);

}
