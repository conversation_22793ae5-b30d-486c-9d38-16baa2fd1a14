package org.example.mybatisplus.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.example.mybatisplus.entity.User;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
public interface IUserService extends IService<User> {
    List<User> selectAll();
    IPage<User> selectByPage(int current, int size);

}
