package org.example.mybatisppus.serviceimpl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.example.mybatisppus.entity.User;
import org.example.mybatisppus.mapper.UserMapper;
import org.example.mybatisppus.service.IUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {

    @Override
    public List<User> selectAll() {
        QueryWrapper<User> queryWrapper=new QueryWrapper<>();
        IPage<User> iPage=new Page<>( 2,1);//current为当前页,每页3条数据
        return baseMapper.selectPage(iPage,queryWrapper).getRecords();
    }
}
