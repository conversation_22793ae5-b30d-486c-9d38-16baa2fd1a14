package org.example.mybatisppus.serviceimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.example.mybatisppus.entity.Admin;
import org.example.mybatisppus.entity.Result;
import org.example.mybatisppus.mapper.AdminMapper;
import org.example.mybatisppus.service.IAdminService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Service
public class AdminServiceImpl extends ServiceImpl<AdminMapper, Admin> implements IAdminService {
    @Override
    public Result register(Admin admin) {
        LambdaQueryWrapper<Admin> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        Admin admin1 = baseMapper.selectOne(lambdaQueryWrapper.eq(Admin::getUsername, admin.getUsername()));
        if(Object.isNull(admin1)){
            if(baseMapper.insert(admin)>0){
                return Result.success(200,"注册成功");
            }else return Result.fail("注册失败");
        }
        return null;
    }
}
