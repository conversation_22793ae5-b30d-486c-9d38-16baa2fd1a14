package org.example.mybatisppus.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import org.example.mybatisppus.entity.User;
import org.example.mybatisppus.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@RestController
@RequestMapping("/user")
public class UserController {
    @Autowired
    private IUserService userService;
    //查询所有
    @RequestMapping("/selectAll")
    public Object selectAll(){
        return userService.selectAll();
    }
    // 分页查询用户
    @RequestMapping("/page")
    public IPage<User> selectByPage(
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size) {
        return userService.selectByPage(current, size);
    }

}
