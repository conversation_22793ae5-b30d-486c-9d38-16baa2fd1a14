package org.example.mybatisppus.controller;


import org.example.mybatisppus.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@RestController
@RequestMapping("/user")
public class UserController {
    @Autowired
    private IUserService userService;
    //查询所有
    @RequestMapping("/selectAll")
    public Object selectAll(){
        return userService.selectAll();
    }

}
