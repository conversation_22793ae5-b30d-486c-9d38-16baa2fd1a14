package org.example.mybatisppus.controller;


import org.example.mybatisppus.entity.Admin;
import org.example.mybatisppus.entity.Result;
import org.example.mybatisppus.service.IAdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@RestController
@CrossOrigin(origins = "*")
@RequestMapping("/admin")
public class AdminController {
    @Autowired
    private IAdminService adminService;
    @RequestMapping("/register")
    public Result register(@RequestBody Admin admin)
    {
        return adminService.register(admin);
    }

}
